import mongoose from "mongoose";

const userSchema=new mongoose.Schema({
    fullName:{
        type :String,
         required:true
    },
    email:{
        type:String,
        required:true,
        unique:true
    },
    password:{
        type:String,
    },
    mobile:{
 type:String,
 require:true,
    },
    role: {
  type: String,
  enum: ["user", "owner", "deliveryBoy"],
  required: true
}

},{timestamps:true});

const User = mongoose.model("user", userSchema);
export default User;