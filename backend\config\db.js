import mongoose from "mongoose";


const connectDb = async () => {
    try {
        console.log('Connecting to MongoDB:', process.env.MONGODB_URL);
        await mongoose.connect(process.env.MONGODB_URL, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log("db connected");
    } catch (error) {
        console.error("db error", error);
        throw error;
    }
};

export default connectDb;