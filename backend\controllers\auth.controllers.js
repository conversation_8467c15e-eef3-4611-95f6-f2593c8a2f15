import genToken from "../config/utils/token.js";
import User from "../models/user.model.js";
import bcrypt from "bcryptjs";


export const signup = async (req, res) => {
  try {
    const { fullname, email, password, mobile, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists." });
    }

    // Password length check
    if (!password || password.length < 6) {
      return res.status(400).json({ message: "Password must be at least 6 characters." });
    }

    // Mobile number length check
    if (!mobile || mobile.length < 10) {
      return res.status(400).json({ message: "Mobile number must be at least 10 digits." });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser= await User.create({
      fullname,
      email,
      password: hashedPassword,
      mobile,
      role
    });

    // Generate token
    const token = await genToken(newUser.id);

    // Set cookie with token (options applied)
    res.cookie("token", token, {
      secure: false,           // Set to true when using HTTPS in production
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 1 week
      httpOnly: true
    });

    // Respond success
    return res.status(201).json({
      message: "User registered successfully.",
      user: newUser
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: `Server error.${error}` });
  }
};


export const signin = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: "Invalid email or password." });
    }

    // Compare entered password with hashed password in DB
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid email or password." });
    }

    // Generate token
    const token = await genToken(user.id);

    // Set cookie with token
    res.cookie("token", token, {
      secure: false,           // Set to true when using HTTPS in production
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 1 week
      httpOnly: true
    });

    // Respond success
    return res.status(200).json({
      message: "Signin successful.",
      user
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: `Server error.${error}` });
  }
};

export const signout = async (req, res) => {
  try {
    res.clearCookie("token");
    return res.status(200).json({ message: "log out successfully" });
  } catch (error) {
    return res.status(500).json({ message: `signout error.${error}` });
  }
};